<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "SucculentLevel"
});

interface SucculentLevel {
  id: number;
  level: number;
  name: string;
  icon: string;
  requiredEnergy: number;
  requiredDays: number;
  growthBonus: number;
  beautyBonus: number;
  hardinessBonus: number;
  specialAbility: string;
  unlockRewards: string[];
  description: string;
  status: number;
  createTime: string;
}

const loading = ref(false);
const tableData = ref<SucculentLevel[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

const formData = reactive({
  id: 0,
  level: 1,
  name: "",
  icon: "",
  requiredEnergy: 0,
  requiredDays: 0,
  growthBonus: 0,
  beautyBonus: 0,
  hardinessBonus: 0,
  specialAbility: "",
  unlockRewards: [] as string[],
  description: "",
  status: 1
});

// 模拟数据
const mockData: SucculentLevel[] = [
  {
    id: 1,
    level: 1,
    name: "幼苗",
    icon: "🌱",
    requiredEnergy: 0,
    requiredDays: 0,
    growthBonus: 0,
    beautyBonus: 0,
    hardinessBonus: 0,
    specialAbility: "无",
    unlockRewards: ["基础浇水", "基础施肥"],
    description: "刚刚发芽的多肉幼苗",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    level: 2,
    name: "小苗",
    icon: "🌿",
    requiredEnergy: 100,
    requiredDays: 7,
    growthBonus: 5,
    beautyBonus: 3,
    hardinessBonus: 2,
    specialAbility: "快速生长",
    unlockRewards: ["营养土", "小花盆"],
    description: "开始茁壮成长的小苗",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 3,
    level: 3,
    name: "成株",
    icon: "🪴",
    requiredEnergy: 300,
    requiredDays: 15,
    growthBonus: 10,
    beautyBonus: 8,
    hardinessBonus: 6,
    specialAbility: "抗旱能力",
    unlockRewards: ["装饰花盆", "特殊肥料", "造型工具"],
    description: "已经成熟的多肉植物",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 4,
    level: 4,
    name: "精品",
    icon: "✨",
    requiredEnergy: 600,
    requiredDays: 30,
    growthBonus: 15,
    beautyBonus: 15,
    hardinessBonus: 12,
    specialAbility: "自我修复",
    unlockRewards: ["精美花盆", "高级肥料", "修剪工具", "繁殖道具"],
    description: "品相优良的精品多肉",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 5,
    level: 5,
    name: "传奇",
    icon: "👑",
    requiredEnergy: 1200,
    requiredDays: 60,
    growthBonus: 25,
    beautyBonus: 25,
    hardinessBonus: 20,
    specialAbility: "完美形态",
    unlockRewards: ["传奇花盆", "神秘肥料", "大师工具", "稀有种子", "展示台"],
    description: "传说级别的完美多肉",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  }
];

const rewardOptions = [
  "基础浇水", "基础施肥", "营养土", "小花盆", "装饰花盆", "特殊肥料", 
  "造型工具", "精美花盆", "高级肥料", "修剪工具", "繁殖道具", 
  "传奇花盆", "神秘肥料", "大师工具", "稀有种子", "展示台"
];

const columns = [
  {
    label: "等级",
    prop: "level",
    minWidth: 80
  },
  {
    label: "名称",
    prop: "name",
    minWidth: 100
  },
  {
    label: "图标",
    prop: "icon",
    minWidth: 80
  },
  {
    label: "所需能量",
    prop: "requiredEnergy",
    minWidth: 100
  },
  {
    label: "所需天数",
    prop: "requiredDays",
    minWidth: 100
  },
  {
    label: "属性加成",
    prop: "bonus",
    minWidth: 150
  },
  {
    label: "特殊能力",
    prop: "specialAbility",
    minWidth: 120
  },
  {
    label: "解锁奖励",
    prop: "unlockRewards",
    minWidth: 200
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 180
  }
];

const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = [...mockData];
    loading.value = false;
  }, 500);
};

const handleAdd = () => {
  dialogTitle.value = "新增等级";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleEdit = (row: SucculentLevel) => {
  dialogTitle.value = "编辑等级";
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDelete = (row: SucculentLevel) => {
  ElMessageBox.confirm(`确定要删除等级 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  });
};

const handleToggleStatus = (row: SucculentLevel) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}等级 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetFormData = () => {
  formData.id = 0;
  formData.level = 1;
  formData.name = "";
  formData.icon = "";
  formData.requiredEnergy = 0;
  formData.requiredDays = 0;
  formData.growthBonus = 0;
  formData.beautyBonus = 0;
  formData.hardinessBonus = 0;
  formData.specialAbility = "";
  formData.unlockRewards = [];
  formData.description = "";
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入等级名称");
    return;
  }
  
  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      tableData.value[index] = {
        ...formData,
        createTime: tableData.value[index].createTime
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const newLevel: SucculentLevel = {
      ...formData,
      id: Date.now(),
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newLevel);
    ElMessage.success("新增成功");
  }
  
  dialogVisible.value = false;
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 表格工具栏 -->
    <PureTableBar title="多肉等级系统" :columns="columns" @refresh="loadData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增等级
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #icon="{ row }">
            <span style="font-size: 24px;">{{ row.icon }}</span>
          </template>

          <template #bonus="{ row }">
            <div class="text-sm">
              <div>成长: +{{ row.growthBonus }}</div>
              <div>美观: +{{ row.beautyBonus }}</div>
              <div>耐性: +{{ row.hardinessBonus }}</div>
            </div>
          </template>

          <template #unlockRewards="{ row }">
            <el-tag
              v-for="reward in row.unlockRewards"
              :key="reward"
              size="small"
              class="mr-1 mb-1"
            >
              {{ reward }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      draggable
    >
      <el-form
        :model="formData"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="等级数值" required>
              <el-input-number
                v-model="formData.level"
                :min="1"
                :max="50"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="等级名称" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入等级名称"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="等级图标">
              <el-input
                v-model="formData.icon"
                placeholder="请输入等级图标(emoji)"
                maxlength="10"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="特殊能力">
              <el-input
                v-model="formData.specialAbility"
                placeholder="请输入特殊能力"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所需能量">
              <el-input-number
                v-model="formData.requiredEnergy"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所需天数">
              <el-input-number
                v-model="formData.requiredDays"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="属性加成">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-2">成长加成: +{{ formData.growthBonus }}</div>
              <el-slider
                v-model="formData.growthBonus"
                :min="0"
                :max="50"
                show-stops
              />
            </el-col>
            <el-col :span="8">
              <div class="mb-2">美观加成: +{{ formData.beautyBonus }}</div>
              <el-slider
                v-model="formData.beautyBonus"
                :min="0"
                :max="50"
                show-stops
              />
            </el-col>
            <el-col :span="8">
              <div class="mb-2">耐性加成: +{{ formData.hardinessBonus }}</div>
              <el-slider
                v-model="formData.hardinessBonus"
                :min="0"
                :max="50"
                show-stops
              />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="解锁奖励">
          <el-select
            v-model="formData.unlockRewards"
            multiple
            placeholder="请选择解锁奖励"
            style="width: 100%"
          >
            <el-option
              v-for="item in rewardOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="等级描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入等级描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
