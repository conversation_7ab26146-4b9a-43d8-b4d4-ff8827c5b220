<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "MeditationCourse"
});

interface Course {
  id: number;
  title: string;
  cover: string;
  description: string;
  instructor: string;
  category: string;
  difficulty: string;
  totalLessons: number;
  totalDuration: number;
  price: number;
  originalPrice: number;
  enrollCount: number;
  rating: number;
  tags: string[];
  status: number;
  isRecommended: boolean;
  createTime: string;
  publishTime: string;
}

const loading = ref(false);
const tableData = ref<Course[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  title: "",
  category: "",
  difficulty: "",
  status: ""
});

// 模拟数据
const mockData: Course[] = [
  {
    id: 1,
    title: "21天正念冥想入门课程",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    description: "从零开始学习正念冥想，21天养成冥想习惯",
    instructor: "李老师",
    category: "正念冥想",
    difficulty: "初级",
    totalLessons: 21,
    totalDuration: 630,
    price: 99,
    originalPrice: 199,
    enrollCount: 1250,
    rating: 4.8,
    tags: ["正念", "入门", "21天"],
    status: 1,
    isRecommended: true,
    createTime: "2024-01-10 10:00:00",
    publishTime: "2024-01-15 09:00:00"
  },
  {
    id: 2,
    title: "深度睡眠改善计划",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    description: "通过专业的睡眠冥想技巧，改善睡眠质量",
    instructor: "王老师",
    category: "睡眠冥想",
    difficulty: "中级",
    totalLessons: 14,
    totalDuration: 840,
    price: 129,
    originalPrice: 259,
    enrollCount: 890,
    rating: 4.6,
    tags: ["睡眠", "改善", "深度"],
    status: 1,
    isRecommended: false,
    createTime: "2024-01-08 14:30:00",
    publishTime: "2024-01-12 20:00:00"
  }
];

const categoryOptions = [
  { label: "正念冥想", value: "正念冥想" },
  { label: "睡眠冥想", value: "睡眠冥想" },
  { label: "专注训练", value: "专注训练" },
  { label: "情绪管理", value: "情绪管理" }
];

const difficultyOptions = [
  { label: "初级", value: "初级" },
  { label: "中级", value: "中级" },
  { label: "高级", value: "高级" }
];

const statusOptions = [
  { label: "已发布", value: 1 },
  { label: "草稿", value: 0 },
  { label: "已下架", value: 2 }
];

const columns = [
  {
    label: "课程封面",
    prop: "cover",
    minWidth: 100
  },
  {
    label: "课程标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "讲师",
    prop: "instructor",
    minWidth: 100
  },
  {
    label: "分类",
    prop: "category",
    minWidth: 120
  },
  {
    label: "难度",
    prop: "difficulty",
    minWidth: 80
  },
  {
    label: "课时/时长",
    prop: "lessons",
    minWidth: 120
  },
  {
    label: "价格",
    prop: "price",
    minWidth: 100
  },
  {
    label: "报名人数",
    prop: "enrollCount",
    minWidth: 100
  },
  {
    label: "评分",
    prop: "rating",
    minWidth: 80
  },
  {
    label: "推荐",
    prop: "isRecommended",
    minWidth: 80
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 280
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (!searchForm.title || item.title.includes(searchForm.title)) &&
             (!searchForm.category || item.category === searchForm.category) &&
             (!searchForm.difficulty || item.difficulty === searchForm.difficulty) &&
             (!searchForm.status || item.status.toString() === searchForm.status);
    });
    pagination.total = tableData.value.length;
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.title = "";
  searchForm.category = "";
  searchForm.difficulty = "";
  searchForm.status = "";
  onSearch();
};

const handleAdd = () => {
  ElMessage.info("跳转到课程创建页面");
};

const handleEdit = (row: Course) => {
  ElMessage.info(`编辑课程: ${row.title}`);
};

const handleManageLessons = (row: Course) => {
  ElMessage.info(`管理课程章节: ${row.title}`);
};

const handleToggleRecommend = (row: Course) => {
  const action = row.isRecommended ? "取消推荐" : "设为推荐";
  ElMessageBox.confirm(`确定要${action}课程 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.isRecommended = !row.isRecommended;
    ElMessage.success(`${action}成功`);
  });
};

const handleToggleStatus = (row: Course) => {
  let action = "";
  let newStatus = 0;
  
  if (row.status === 1) {
    action = "下架";
    newStatus = 2;
  } else {
    action = "发布";
    newStatus = 1;
  }
  
  ElMessageBox.confirm(`确定要${action}课程 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = newStatus;
    ElMessage.success(`${action}成功`);
  });
};

const handleDelete = (row: Course) => {
  ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    ElMessage.success("删除成功");
  });
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}h${mins}m` : `${mins}m`;
};

const getStatusTag = (status: number) => {
  const statusMap = {
    0: { type: 'info', text: '草稿' },
    1: { type: 'success', text: '已发布' },
    2: { type: 'warning', text: '已下架' }
  };
  return statusMap[status] || { type: 'info', text: '未知' };
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="课程标题：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入课程标题"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="分类：" prop="category">
        <el-select
          v-model="searchForm.category"
          placeholder="请选择分类"
          clearable
          class="!w-[150px]"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="难度：" prop="difficulty">
        <el-select
          v-model="searchForm.difficulty"
          placeholder="请选择难度"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in difficultyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="课程管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增课程
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #cover="{ row }">
            <el-image
              :src="row.cover"
              fit="cover"
              style="width: 80px; height: 60px; border-radius: 4px;"
              :preview-src-list="[row.cover]"
              preview-teleported
            />
          </template>

          <template #lessons="{ row }">
            <div>
              <div>{{ row.totalLessons }}课时</div>
              <div class="text-gray-500 text-sm">{{ formatDuration(row.totalDuration) }}</div>
            </div>
          </template>

          <template #price="{ row }">
            <div>
              <div class="text-red-500 font-bold">¥{{ row.price }}</div>
              <div class="text-gray-400 line-through text-sm">¥{{ row.originalPrice }}</div>
            </div>
          </template>

          <template #rating="{ row }">
            <el-rate
              v-model="row.rating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>

          <template #isRecommended="{ row }">
            <el-tag :type="row.isRecommended ? 'success' : 'info'">
              {{ row.isRecommended ? '推荐' : '普通' }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="getStatusTag(row.status).type">
              {{ getStatusTag(row.status).text }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:list')"
              @click="handleManageLessons(row)"
            >
              章节
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.isRecommended ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.isRecommended ? 'ep:star-filled' : 'ep:star')"
              @click="handleToggleRecommend(row)"
            >
              {{ row.isRecommended ? '取消推荐' : '推荐' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:bottom' : 'ep:top')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '下架' : '发布' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
