<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "SucculentGrowth"
});

interface GrowthRecord {
  id: number;
  userId: number;
  username: string;
  avatar: string;
  succulentId: number;
  succulentName: string;
  succulentImage: string;
  variety: string;
  currentLevel: number;
  currentExp: number;
  maxExp: number;
  totalEnergy: number;
  growthDays: number;
  lastWaterTime: string;
  lastFertilizeTime: string;
  healthStatus: string;
  attributes: {
    beauty: number;
    hardiness: number;
    growth: number;
  };
  achievements: string[];
  createTime: string;
  updateTime: string;
}

const loading = ref(false);
const tableData = ref<GrowthRecord[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  username: "",
  succulentName: "",
  variety: "",
  level: "",
  healthStatus: ""
});

// 模拟数据
const mockData: GrowthRecord[] = [
  {
    id: 1,
    userId: 1001,
    username: "冥想小白",
    avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    succulentId: 1,
    succulentName: "我的小玉露",
    succulentImage: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    variety: "玉露",
    currentLevel: 3,
    currentExp: 150,
    maxExp: 300,
    totalEnergy: 850,
    growthDays: 25,
    lastWaterTime: "2024-01-20 08:30:00",
    lastFertilizeTime: "2024-01-18 10:15:00",
    healthStatus: "健康",
    attributes: {
      beauty: 85,
      hardiness: 70,
      growth: 60
    },
    achievements: ["初次开花", "连续照料7天", "等级达到3级"],
    createTime: "2023-12-25 14:20:00",
    updateTime: "2024-01-20 08:30:00"
  },
  {
    id: 2,
    userId: 1002,
    username: "静心达人",
    avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    succulentId: 2,
    succulentName: "胧月宝宝",
    succulentImage: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    variety: "胧月",
    currentLevel: 5,
    currentExp: 420,
    maxExp: 500,
    totalEnergy: 1250,
    growthDays: 45,
    lastWaterTime: "2024-01-19 19:45:00",
    lastFertilizeTime: "2024-01-19 19:45:00",
    healthStatus: "茁壮",
    attributes: {
      beauty: 92,
      hardiness: 88,
      growth: 85
    },
    achievements: ["初次开花", "连续照料7天", "连续照料30天", "等级达到5级", "完美形态"],
    createTime: "2023-12-05 09:10:00",
    updateTime: "2024-01-19 19:45:00"
  },
  {
    id: 3,
    userId: 1003,
    username: "新手园丁",
    avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
    succulentId: 3,
    succulentName: "小胧月",
    succulentImage: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    variety: "胧月",
    currentLevel: 1,
    currentExp: 30,
    maxExp: 100,
    totalEnergy: 180,
    growthDays: 5,
    lastWaterTime: "2024-01-18 16:20:00",
    lastFertilizeTime: "",
    healthStatus: "需要关注",
    attributes: {
      beauty: 45,
      hardiness: 50,
      growth: 40
    },
    achievements: ["新手上路"],
    createTime: "2024-01-15 11:30:00",
    updateTime: "2024-01-18 16:20:00"
  }
];

const varietyOptions = [
  { label: "玉露", value: "玉露" },
  { label: "胧月", value: "胧月" },
  { label: "生石花", value: "生石花" },
  { label: "白牡丹", value: "白牡丹" }
];

const levelOptions = [
  { label: "1级", value: "1" },
  { label: "2级", value: "2" },
  { label: "3级", value: "3" },
  { label: "4级", value: "4" },
  { label: "5级", value: "5" }
];

const healthStatusOptions = [
  { label: "健康", value: "健康" },
  { label: "茁壮", value: "茁壮" },
  { label: "需要关注", value: "需要关注" },
  { label: "生病", value: "生病" }
];

const columns = [
  {
    label: "用户信息",
    prop: "user",
    minWidth: 150
  },
  {
    label: "多肉信息",
    prop: "succulent",
    minWidth: 200
  },
  {
    label: "等级/经验",
    prop: "level",
    minWidth: 120
  },
  {
    label: "属性值",
    prop: "attributes",
    minWidth: 150
  },
  {
    label: "成长天数",
    prop: "growthDays",
    minWidth: 100
  },
  {
    label: "健康状态",
    prop: "healthStatus",
    minWidth: 100
  },
  {
    label: "最后照料",
    prop: "lastCare",
    minWidth: 160
  },
  {
    label: "成就",
    prop: "achievements",
    minWidth: 200
  },
  {
    label: "操作",
    fixed: "right",
    width: 120
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (!searchForm.username || item.username.includes(searchForm.username)) &&
             (!searchForm.succulentName || item.succulentName.includes(searchForm.succulentName)) &&
             (!searchForm.variety || item.variety === searchForm.variety) &&
             (!searchForm.level || item.currentLevel.toString() === searchForm.level) &&
             (!searchForm.healthStatus || item.healthStatus === searchForm.healthStatus);
    });
    pagination.total = tableData.value.length;
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.username = "";
  searchForm.succulentName = "";
  searchForm.variety = "";
  searchForm.level = "";
  searchForm.healthStatus = "";
  onSearch();
};

const handleViewDetail = (row: GrowthRecord) => {
  ElMessage.info(`查看 ${row.username} 的 ${row.succulentName} 详细信息`);
};

const getHealthStatusTag = (status: string) => {
  const statusMap = {
    '健康': { type: 'success', color: '#67C23A' },
    '茁壮': { type: 'primary', color: '#409EFF' },
    '需要关注': { type: 'warning', color: '#E6A23C' },
    '生病': { type: 'danger', color: '#F56C6C' }
  };
  return statusMap[status] || { type: 'info', color: '#909399' };
};

const calculateProgress = (current: number, max: number) => {
  return Math.round((current / max) * 100);
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="用户名：" prop="username">
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
          class="!w-[150px]"
        />
      </el-form-item>
      <el-form-item label="多肉名称：" prop="succulentName">
        <el-input
          v-model="searchForm.succulentName"
          placeholder="请输入多肉名称"
          clearable
          class="!w-[150px]"
        />
      </el-form-item>
      <el-form-item label="品种：" prop="variety">
        <el-select
          v-model="searchForm.variety"
          placeholder="请选择品种"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in varietyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="等级：" prop="level">
        <el-select
          v-model="searchForm.level"
          placeholder="请选择等级"
          clearable
          class="!w-[100px]"
        >
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="健康状态：" prop="healthStatus">
        <el-select
          v-model="searchForm.healthStatus"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in healthStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="多肉成长记录" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="success"
          :icon="useRenderIcon('ep:download')"
        >
          导出数据
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #user="{ row }">
            <div class="flex items-center">
              <el-avatar :size="40" :src="row.avatar" class="mr-3" />
              <div>
                <div class="font-medium">{{ row.username }}</div>
                <div class="text-sm text-gray-500">ID: {{ row.userId }}</div>
              </div>
            </div>
          </template>

          <template #succulent="{ row }">
            <div class="flex items-center">
              <el-image
                :src="row.succulentImage"
                fit="cover"
                style="width: 50px; height: 50px; border-radius: 8px;"
                class="mr-3"
              />
              <div>
                <div class="font-medium">{{ row.succulentName }}</div>
                <div class="text-sm text-gray-500">{{ row.variety }}</div>
                <div class="text-xs text-gray-400">总能量: {{ row.totalEnergy }}</div>
              </div>
            </div>
          </template>

          <template #level="{ row }">
            <div>
              <div class="font-medium text-lg">Lv.{{ row.currentLevel }}</div>
              <el-progress
                :percentage="calculateProgress(row.currentExp, row.maxExp)"
                :stroke-width="6"
                :show-text="false"
                class="mt-1"
              />
              <div class="text-xs text-gray-500 mt-1">
                {{ row.currentExp }}/{{ row.maxExp }}
              </div>
            </div>
          </template>

          <template #attributes="{ row }">
            <div class="text-sm">
              <div class="flex items-center mb-1">
                <span class="w-8">美观</span>
                <el-progress
                  :percentage="row.attributes.beauty"
                  :stroke-width="4"
                  :show-text="false"
                  class="flex-1 mx-2"
                />
                <span class="w-6 text-xs">{{ row.attributes.beauty }}</span>
              </div>
              <div class="flex items-center mb-1">
                <span class="w-8">耐性</span>
                <el-progress
                  :percentage="row.attributes.hardiness"
                  :stroke-width="4"
                  :show-text="false"
                  color="#67C23A"
                  class="flex-1 mx-2"
                />
                <span class="w-6 text-xs">{{ row.attributes.hardiness }}</span>
              </div>
              <div class="flex items-center">
                <span class="w-8">成长</span>
                <el-progress
                  :percentage="row.attributes.growth"
                  :stroke-width="4"
                  :show-text="false"
                  color="#E6A23C"
                  class="flex-1 mx-2"
                />
                <span class="w-6 text-xs">{{ row.attributes.growth }}</span>
              </div>
            </div>
          </template>

          <template #healthStatus="{ row }">
            <el-tag :type="getHealthStatusTag(row.healthStatus).type">
              {{ row.healthStatus }}
            </el-tag>
          </template>

          <template #lastCare="{ row }">
            <div class="text-sm">
              <div v-if="row.lastWaterTime">
                <span class="text-blue-500">💧</span>
                {{ row.lastWaterTime.split(' ')[0] }}
              </div>
              <div v-if="row.lastFertilizeTime" class="mt-1">
                <span class="text-green-500">🌱</span>
                {{ row.lastFertilizeTime.split(' ')[0] }}
              </div>
              <div v-if="!row.lastFertilizeTime" class="mt-1 text-gray-400">
                未施肥
              </div>
            </div>
          </template>

          <template #achievements="{ row }">
            <div class="max-w-48">
              <el-tag
                v-for="achievement in row.achievements.slice(0, 3)"
                :key="achievement"
                size="small"
                type="success"
                class="mr-1 mb-1"
              >
                {{ achievement }}
              </el-tag>
              <div v-if="row.achievements.length > 3" class="text-xs text-gray-500 mt-1">
                +{{ row.achievements.length - 3 }}个成就
              </div>
            </div>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
