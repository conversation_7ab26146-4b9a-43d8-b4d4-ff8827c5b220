<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "MeditationContent"
});

interface Content {
  id: number;
  title: string;
  type: string;
  category: string;
  tags: string[];
  duration: number;
  cover: string;
  audioUrl: string;
  description: string;
  author: string;
  playCount: number;
  likeCount: number;
  status: number;
  auditStatus: number;
  createTime: string;
  publishTime: string;
}

const loading = ref(false);
const tableData = ref<Content[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  title: "",
  type: "",
  category: "",
  status: "",
  auditStatus: ""
});

// 模拟数据
const mockData: Content[] = [
  {
    id: 1,
    title: "晨间冥想：开启美好一天",
    type: "冥想",
    category: "正念冥想",
    tags: ["晨间", "正念", "放松"],
    duration: 600,
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    audioUrl: "https://example.com/audio1.mp3",
    description: "通过晨间冥想，帮助您以平静的心态开始新的一天",
    author: "冥想导师A",
    playCount: 1250,
    likeCount: 89,
    status: 1,
    auditStatus: 2,
    createTime: "2024-01-15 10:30:00",
    publishTime: "2024-01-16 09:00:00"
  },
  {
    id: 2,
    title: "深度睡眠引导",
    type: "睡眠",
    category: "睡眠冥想",
    tags: ["睡眠", "放松", "深度"],
    duration: 1800,
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    audioUrl: "https://example.com/audio2.mp3",
    description: "帮助您快速进入深度睡眠状态",
    author: "冥想导师B",
    playCount: 2100,
    likeCount: 156,
    status: 1,
    auditStatus: 2,
    createTime: "2024-01-14 15:20:00",
    publishTime: "2024-01-15 20:00:00"
  }
];

const typeOptions = [
  { label: "冥想", value: "冥想" },
  { label: "睡眠", value: "睡眠" },
  { label: "白噪音", value: "白噪音" }
];

const statusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 }
];

const auditStatusOptions = [
  { label: "待审核", value: 0 },
  { label: "审核中", value: 1 },
  { label: "已通过", value: 2 },
  { label: "已拒绝", value: 3 }
];

const columns = [
  {
    label: "封面",
    prop: "cover",
    minWidth: 80
  },
  {
    label: "标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 80
  },
  {
    label: "分类",
    prop: "category",
    minWidth: 120
  },
  {
    label: "标签",
    prop: "tags",
    minWidth: 150
  },
  {
    label: "时长",
    prop: "duration",
    minWidth: 80
  },
  {
    label: "作者",
    prop: "author",
    minWidth: 100
  },
  {
    label: "播放量",
    prop: "playCount",
    minWidth: 80
  },
  {
    label: "点赞数",
    prop: "likeCount",
    minWidth: 80
  },
  {
    label: "审核状态",
    prop: "auditStatus",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 240
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (!searchForm.title || item.title.includes(searchForm.title)) &&
             (!searchForm.type || item.type === searchForm.type) &&
             (!searchForm.category || item.category.includes(searchForm.category)) &&
             (!searchForm.status || item.status.toString() === searchForm.status) &&
             (!searchForm.auditStatus || item.auditStatus.toString() === searchForm.auditStatus);
    });
    pagination.total = tableData.value.length;
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.title = "";
  searchForm.type = "";
  searchForm.category = "";
  searchForm.status = "";
  searchForm.auditStatus = "";
  onSearch();
};

const handleAdd = () => {
  ElMessage.info("跳转到内容创建页面");
};

const handleEdit = (row: Content) => {
  ElMessage.info(`编辑内容: ${row.title}`);
};

const handleDelete = (row: Content) => {
  ElMessageBox.confirm(`确定要删除内容 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    ElMessage.success("删除成功");
  });
};

const handleToggleStatus = (row: Content) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}内容 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const handlePreview = (row: Content) => {
  ElMessage.info(`预览内容: ${row.title}`);
};

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const getAuditStatusTag = (status: number) => {
  const statusMap = {
    0: { type: 'info', text: '待审核' },
    1: { type: 'warning', text: '审核中' },
    2: { type: 'success', text: '已通过' },
    3: { type: 'danger', text: '已拒绝' }
  };
  return statusMap[status] || { type: 'info', text: '未知' };
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="标题：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入内容标题"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="searchForm.type"
          placeholder="请选择类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类：" prop="category">
        <el-input
          v-model="searchForm.category"
          placeholder="请输入分类"
          clearable
          class="!w-[150px]"
        />
      </el-form-item>
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select
          v-model="searchForm.auditStatus"
          placeholder="请选择审核状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in auditStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="冥想内容管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增内容
        </el-button>
        <el-button
          type="success"
          :icon="useRenderIcon('ep:upload')"
        >
          批量导入
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #cover="{ row }">
            <el-image
              :src="row.cover"
              fit="cover"
              style="width: 60px; height: 40px; border-radius: 4px;"
              :preview-src-list="[row.cover]"
              preview-teleported
            />
          </template>

          <template #tags="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              class="mr-1"
            >
              {{ tag }}
            </el-tag>
          </template>

          <template #duration="{ row }">
            {{ formatDuration(row.duration) }}
          </template>

          <template #auditStatus="{ row }">
            <el-tag :type="getAuditStatusTag(row.auditStatus).type">
              {{ getAuditStatusTag(row.auditStatus).text }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handlePreview(row)"
            >
              预览
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
}
</style>
