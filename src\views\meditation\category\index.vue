<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "MeditationCategory"
});

interface Category {
  id: number;
  name: string;
  type: string;
  parentId: number;
  parentName: string;
  icon: string;
  description: string;
  sort: number;
  contentCount: number;
  status: number;
  createTime: string;
}

const loading = ref(false);
const tableData = ref<Category[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

const formData = reactive({
  id: 0,
  name: "",
  type: "冥想",
  parentId: 0,
  icon: "",
  description: "",
  sort: 0,
  status: 1
});

// 模拟数据
const mockData: Category[] = [
  {
    id: 1,
    name: "正念冥想",
    type: "冥想",
    parentId: 0,
    parentName: "",
    icon: "🧘",
    description: "专注当下，培养正念意识",
    sort: 1,
    contentCount: 25,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "呼吸冥想",
    type: "冥想",
    parentId: 1,
    parentName: "正念冥想",
    icon: "💨",
    description: "通过呼吸练习达到放松状态",
    sort: 1,
    contentCount: 12,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 3,
    name: "睡眠冥想",
    type: "睡眠",
    parentId: 0,
    parentName: "",
    icon: "🌙",
    description: "帮助改善睡眠质量",
    sort: 2,
    contentCount: 18,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 4,
    name: "自然白噪音",
    type: "白噪音",
    parentId: 0,
    parentName: "",
    icon: "🌿",
    description: "自然环境声音",
    sort: 3,
    contentCount: 30,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  }
];

const typeOptions = [
  { label: "冥想", value: "冥想" },
  { label: "睡眠", value: "睡眠" },
  { label: "白噪音", value: "白噪音" }
];

const columns = [
  {
    label: "分类名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 100
  },
  {
    label: "父级分类",
    prop: "parentName",
    minWidth: 120
  },
  {
    label: "图标",
    prop: "icon",
    minWidth: 80
  },
  {
    label: "描述",
    prop: "description",
    minWidth: 200
  },
  {
    label: "排序",
    prop: "sort",
    minWidth: 80
  },
  {
    label: "内容数量",
    prop: "contentCount",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 200
  }
];

const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = [...mockData];
    loading.value = false;
  }, 500);
};

const handleAdd = () => {
  dialogTitle.value = "新增分类";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

const handleEdit = (row: Category) => {
  dialogTitle.value = "编辑分类";
  isEdit.value = true;
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    type: row.type,
    parentId: row.parentId,
    icon: row.icon,
    description: row.description,
    sort: row.sort,
    status: row.status
  });
  dialogVisible.value = true;
};

const handleDelete = (row: Category) => {
  if (row.contentCount > 0) {
    ElMessage.warning("该分类下还有内容，无法删除");
    return;
  }
  
  ElMessageBox.confirm(`确定要删除分类 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  });
};

const handleToggleStatus = (row: Category) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}分类 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetForm = () => {
  formData.id = 0;
  formData.name = "";
  formData.type = "冥想";
  formData.parentId = 0;
  formData.icon = "";
  formData.description = "";
  formData.sort = 0;
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入分类名称");
    return;
  }
  
  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      const parentCategory = tableData.value.find(item => item.id === formData.parentId);
      tableData.value[index] = {
        ...tableData.value[index],
        ...formData,
        parentName: parentCategory ? parentCategory.name : ""
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const parentCategory = tableData.value.find(item => item.id === formData.parentId);
    const newCategory: Category = {
      ...formData,
      id: Date.now(),
      parentName: parentCategory ? parentCategory.name : "",
      contentCount: 0,
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newCategory);
    ElMessage.success("新增成功");
  }
  
  dialogVisible.value = false;
};

// 获取父级分类选项（只显示一级分类）
const getParentOptions = () => {
  return tableData.value.filter(item => item.parentId === 0 && item.type === formData.type);
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 表格工具栏 -->
    <PureTableBar title="分类管理" :columns="columns" @refresh="loadData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增分类
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #icon="{ row }">
            <span style="font-size: 20px;">{{ row.icon }}</span>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      draggable
    >
      <el-form
        :model="formData"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="分类名称" required>
          <el-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="类型" required>
          <el-select
            v-model="formData.type"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="父级分类">
          <el-select
            v-model="formData.parentId"
            placeholder="请选择父级分类（可选）"
            clearable
            style="width: 100%"
          >
            <el-option label="无" :value="0" />
            <el-option
              v-for="item in getParentOptions()"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="图标">
          <el-input
            v-model="formData.icon"
            placeholder="请输入图标(emoji)"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
</style>
