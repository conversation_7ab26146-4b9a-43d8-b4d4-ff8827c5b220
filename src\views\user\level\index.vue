<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "UserLevel"
});

interface UserLevel {
  id: number;
  level: number;
  name: string;
  icon: string;
  requiredDays: number;
  requiredTime: number;
  privileges: string[];
  description: string;
  createTime: string;
  status: number;
}

const loading = ref(false);
const tableData = ref<UserLevel[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

const formData = reactive({
  id: 0,
  level: 1,
  name: "",
  icon: "",
  requiredDays: 0,
  requiredTime: 0,
  privileges: [] as string[],
  description: "",
  status: 1
});

// 模拟数据
const mockData: UserLevel[] = [
  {
    id: 1,
    level: 1,
    name: "初心者",
    icon: "🌱",
    requiredDays: 0,
    requiredTime: 0,
    privileges: ["基础冥想内容", "多肉种植"],
    description: "刚开始冥想之旅的用户",
    createTime: "2024-01-01 10:00:00",
    status: 1
  },
  {
    id: 2,
    level: 2,
    name: "进阶者",
    icon: "🌿",
    requiredDays: 7,
    requiredTime: 210,
    privileges: ["基础冥想内容", "进阶冥想内容", "多肉进阶品种"],
    description: "有一定冥想基础的用户",
    createTime: "2024-01-01 10:00:00",
    status: 1
  },
  {
    id: 3,
    level: 3,
    name: "专家",
    icon: "🍃",
    requiredDays: 30,
    requiredTime: 900,
    privileges: ["所有冥想内容", "专属课程", "稀有多肉品种"],
    description: "冥想经验丰富的用户",
    createTime: "2024-01-01 10:00:00",
    status: 1
  },
  {
    id: 4,
    level: 4,
    name: "大师",
    icon: "🌳",
    requiredDays: 100,
    requiredTime: 3000,
    privileges: ["所有内容", "专属定制", "限量多肉", "社区管理"],
    description: "冥想大师级别用户",
    createTime: "2024-01-01 10:00:00",
    status: 1
  }
];

const privilegeOptions = [
  "基础冥想内容",
  "进阶冥想内容",
  "专属课程",
  "多肉种植",
  "多肉进阶品种",
  "稀有多肉品种",
  "限量多肉",
  "社区管理",
  "专属定制"
];

const columns = [
  {
    label: "等级",
    prop: "level",
    minWidth: 80
  },
  {
    label: "等级名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "图标",
    prop: "icon",
    minWidth: 80
  },
  {
    label: "所需天数",
    prop: "requiredDays",
    minWidth: 100
  },
  {
    label: "所需时长(分钟)",
    prop: "requiredTime",
    minWidth: 140
  },
  {
    label: "等级权益",
    prop: "privileges",
    minWidth: 200
  },
  {
    label: "描述",
    prop: "description",
    minWidth: 150
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 180
  }
];

const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = [...mockData];
    loading.value = false;
  }, 500);
};

const handleAdd = () => {
  dialogTitle.value = "新增等级";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

const handleEdit = (row: UserLevel) => {
  dialogTitle.value = "编辑等级";
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDelete = (row: UserLevel) => {
  ElMessageBox.confirm(`确定要删除等级 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  });
};

const handleToggleStatus = (row: UserLevel) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}等级 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetForm = () => {
  formData.id = 0;
  formData.level = 1;
  formData.name = "";
  formData.icon = "";
  formData.requiredDays = 0;
  formData.requiredTime = 0;
  formData.privileges = [];
  formData.description = "";
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入等级名称");
    return;
  }
  
  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      tableData.value[index] = {
        ...formData,
        createTime: tableData.value[index].createTime
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const newLevel: UserLevel = {
      ...formData,
      id: Date.now(),
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newLevel);
    ElMessage.success("新增成功");
  }
  
  dialogVisible.value = false;
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 表格工具栏 -->
    <PureTableBar title="用户等级管理" :columns="columns" @refresh="loadData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增等级
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #icon="{ row }">
            <span style="font-size: 24px;">{{ row.icon }}</span>
          </template>

          <template #privileges="{ row }">
            <el-tag
              v-for="privilege in row.privileges"
              :key="privilege"
              size="small"
              class="mr-1 mb-1"
            >
              {{ privilege }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      draggable
    >
      <el-form
        :model="formData"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="等级数值" required>
          <el-input-number
            v-model="formData.level"
            :min="1"
            :max="10"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="等级名称" required>
          <el-input
            v-model="formData.name"
            placeholder="请输入等级名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="等级图标">
          <el-input
            v-model="formData.icon"
            placeholder="请输入等级图标(emoji)"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item label="所需天数">
          <el-input-number
            v-model="formData.requiredDays"
            :min="0"
            controls-position="right"
          />
          <span class="ml-2 text-gray-500">天</span>
        </el-form-item>
        <el-form-item label="所需时长">
          <el-input-number
            v-model="formData.requiredTime"
            :min="0"
            controls-position="right"
          />
          <span class="ml-2 text-gray-500">分钟</span>
        </el-form-item>
        <el-form-item label="等级权益">
          <el-select
            v-model="formData.privileges"
            multiple
            placeholder="请选择等级权益"
            style="width: 100%"
          >
            <el-option
              v-for="item in privilegeOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="等级描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入等级描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
