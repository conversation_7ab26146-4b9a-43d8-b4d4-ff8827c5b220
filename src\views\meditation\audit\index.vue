<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "MeditationAudit"
});

interface AuditItem {
  id: number;
  title: string;
  type: string;
  category: string;
  author: string;
  cover: string;
  duration: number;
  submitTime: string;
  auditStatus: number;
  auditTime: string;
  auditor: string;
  rejectReason: string;
  priority: number;
}

const loading = ref(false);
const tableData = ref<AuditItem[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  title: "",
  type: "",
  auditStatus: "",
  author: ""
});

const auditDialogVisible = ref(false);
const currentAuditItem = ref<AuditItem | null>(null);
const auditForm = reactive({
  status: 2,
  reason: ""
});

// 模拟数据
const mockData: AuditItem[] = [
  {
    id: 1,
    title: "夜晚放松冥想",
    type: "冥想",
    category: "睡眠冥想",
    author: "创作者A",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    duration: 900,
    submitTime: "2024-01-20 14:30:00",
    auditStatus: 0,
    auditTime: "",
    auditor: "",
    rejectReason: "",
    priority: 1
  },
  {
    id: 2,
    title: "专注力提升训练",
    type: "冥想",
    category: "专注训练",
    author: "创作者B",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    duration: 1200,
    submitTime: "2024-01-20 10:15:00",
    auditStatus: 1,
    auditTime: "",
    auditor: "审核员1",
    rejectReason: "",
    priority: 2
  },
  {
    id: 3,
    title: "森林白噪音",
    type: "白噪音",
    category: "自然声音",
    author: "创作者C",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    duration: 3600,
    submitTime: "2024-01-19 16:45:00",
    auditStatus: 2,
    auditTime: "2024-01-20 09:30:00",
    auditor: "审核员2",
    rejectReason: "",
    priority: 3
  },
  {
    id: 4,
    title: "不合规内容示例",
    type: "冥想",
    category: "正念冥想",
    author: "创作者D",
    cover: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    duration: 600,
    submitTime: "2024-01-19 11:20:00",
    auditStatus: 3,
    auditTime: "2024-01-19 15:10:00",
    auditor: "审核员1",
    rejectReason: "内容质量不符合标准，音频存在杂音",
    priority: 1
  }
];

const typeOptions = [
  { label: "冥想", value: "冥想" },
  { label: "睡眠", value: "睡眠" },
  { label: "白噪音", value: "白噪音" }
];

const auditStatusOptions = [
  { label: "待审核", value: 0 },
  { label: "审核中", value: 1 },
  { label: "已通过", value: 2 },
  { label: "已拒绝", value: 3 }
];

const priorityOptions = [
  { label: "高", value: 1 },
  { label: "中", value: 2 },
  { label: "低", value: 3 }
];

const columns = [
  {
    label: "封面",
    prop: "cover",
    minWidth: 80
  },
  {
    label: "标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 80
  },
  {
    label: "分类",
    prop: "category",
    minWidth: 120
  },
  {
    label: "作者",
    prop: "author",
    minWidth: 100
  },
  {
    label: "时长",
    prop: "duration",
    minWidth: 80
  },
  {
    label: "优先级",
    prop: "priority",
    minWidth: 80
  },
  {
    label: "提交时间",
    prop: "submitTime",
    minWidth: 160
  },
  {
    label: "审核状态",
    prop: "auditStatus",
    minWidth: 100
  },
  {
    label: "审核员",
    prop: "auditor",
    minWidth: 100
  },
  {
    label: "操作",
    fixed: "right",
    width: 200
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (!searchForm.title || item.title.includes(searchForm.title)) &&
             (!searchForm.type || item.type === searchForm.type) &&
             (!searchForm.author || item.author.includes(searchForm.author)) &&
             (!searchForm.auditStatus || item.auditStatus.toString() === searchForm.auditStatus);
    });
    pagination.total = tableData.value.length;
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.title = "";
  searchForm.type = "";
  searchForm.auditStatus = "";
  searchForm.author = "";
  onSearch();
};

const handlePreview = (row: AuditItem) => {
  ElMessage.info(`预览内容: ${row.title}`);
};

const handleAudit = (row: AuditItem) => {
  currentAuditItem.value = row;
  auditForm.status = 2;
  auditForm.reason = "";
  auditDialogVisible.value = true;
};

const handleQuickApprove = (row: AuditItem) => {
  ElMessageBox.confirm(`确定要快速通过 ${row.title} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "success"
  }).then(() => {
    row.auditStatus = 2;
    row.auditTime = new Date().toLocaleString();
    row.auditor = "当前用户";
    ElMessage.success("审核通过");
  });
};

const handleQuickReject = (row: AuditItem) => {
  ElMessageBox.prompt("请输入拒绝原因", "快速拒绝", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputType: "textarea",
    inputValidator: (value) => {
      if (!value) {
        return "请输入拒绝原因";
      }
      return true;
    }
  }).then(({ value }) => {
    row.auditStatus = 3;
    row.auditTime = new Date().toLocaleString();
    row.auditor = "当前用户";
    row.rejectReason = value;
    ElMessage.success("已拒绝");
  });
};

const submitAudit = () => {
  if (auditForm.status === 3 && !auditForm.reason) {
    ElMessage.warning("拒绝时必须填写原因");
    return;
  }
  
  if (currentAuditItem.value) {
    currentAuditItem.value.auditStatus = auditForm.status;
    currentAuditItem.value.auditTime = new Date().toLocaleString();
    currentAuditItem.value.auditor = "当前用户";
    if (auditForm.status === 3) {
      currentAuditItem.value.rejectReason = auditForm.reason;
    }
    
    const statusText = auditForm.status === 2 ? "通过" : "拒绝";
    ElMessage.success(`审核${statusText}成功`);
    auditDialogVisible.value = false;
  }
};

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const getAuditStatusTag = (status: number) => {
  const statusMap = {
    0: { type: 'info', text: '待审核' },
    1: { type: 'warning', text: '审核中' },
    2: { type: 'success', text: '已通过' },
    3: { type: 'danger', text: '已拒绝' }
  };
  return statusMap[status] || { type: 'info', text: '未知' };
};

const getPriorityTag = (priority: number) => {
  const priorityMap = {
    1: { type: 'danger', text: '高' },
    2: { type: 'warning', text: '中' },
    3: { type: 'success', text: '低' }
  };
  return priorityMap[priority] || { type: 'info', text: '未知' };
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="标题：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入内容标题"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="searchForm.type"
          placeholder="请选择类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="作者：" prop="author">
        <el-input
          v-model="searchForm.author"
          placeholder="请输入作者"
          clearable
          class="!w-[150px]"
        />
      </el-form-item>
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select
          v-model="searchForm.auditStatus"
          placeholder="请选择审核状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in auditStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="内容审核" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #cover="{ row }">
            <el-image
              :src="row.cover"
              fit="cover"
              style="width: 60px; height: 40px; border-radius: 4px;"
              :preview-src-list="[row.cover]"
              preview-teleported
            />
          </template>

          <template #duration="{ row }">
            {{ formatDuration(row.duration) }}
          </template>

          <template #priority="{ row }">
            <el-tag :type="getPriorityTag(row.priority).type" size="small">
              {{ getPriorityTag(row.priority).text }}
            </el-tag>
          </template>

          <template #auditStatus="{ row }">
            <div>
              <el-tag :type="getAuditStatusTag(row.auditStatus).type">
                {{ getAuditStatusTag(row.auditStatus).text }}
              </el-tag>
              <div v-if="row.rejectReason" class="text-red-500 text-xs mt-1">
                {{ row.rejectReason }}
              </div>
            </div>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handlePreview(row)"
            >
              预览
            </el-button>
            <template v-if="row.auditStatus === 0 || row.auditStatus === 1">
              <el-button
                class="reset-margin"
                link
                type="success"
                :size="size"
                :icon="useRenderIcon('ep:check')"
                @click="handleQuickApprove(row)"
              >
                通过
              </el-button>
              <el-button
                class="reset-margin"
                link
                type="danger"
                :size="size"
                :icon="useRenderIcon('ep:close')"
                @click="handleQuickReject(row)"
              >
                拒绝
              </el-button>
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon('ep:edit')"
                @click="handleAudit(row)"
              >
                详细审核
              </el-button>
            </template>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      title="内容审核"
      width="600px"
      draggable
    >
      <div v-if="currentAuditItem">
        <div class="mb-4">
          <h4 class="text-lg font-medium mb-2">{{ currentAuditItem.title }}</h4>
          <div class="flex items-center space-x-4 text-sm text-gray-600">
            <span>类型：{{ currentAuditItem.type }}</span>
            <span>分类：{{ currentAuditItem.category }}</span>
            <span>作者：{{ currentAuditItem.author }}</span>
            <span>时长：{{ formatDuration(currentAuditItem.duration) }}</span>
          </div>
        </div>
        
        <el-form :model="auditForm" label-width="100px">
          <el-form-item label="审核结果" required>
            <el-radio-group v-model="auditForm.status">
              <el-radio :value="2">通过</el-radio>
              <el-radio :value="3">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item 
            label="审核意见" 
            :required="auditForm.status === 3"
          >
            <el-input
              v-model="auditForm.reason"
              type="textarea"
              :rows="4"
              :placeholder="auditForm.status === 3 ? '请输入拒绝原因' : '请输入审核意见（可选）'"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">提交审核</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
